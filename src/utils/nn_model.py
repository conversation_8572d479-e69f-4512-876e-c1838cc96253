#%% ---- Import Required Libraries ----
import json
import inspect
import os
from datetime import datetime

import numpy as np
import tensorflow as tf

from utils.funcs import normalize_stress_components

#%% ---- Random Seed ----
seed=42
tf.random.set_seed(seed)

# %% Set the default floating-point precision for Keras backend operations.
tf.keras.backend.set_floatx('float64')
GLOBAL_DTYPE = tf.float64

#%% ---- Activation Functions ----
# Define the customized activation functions
def custom_act_v1 (x):
    """Custom activation function: ELU(x^2)"""
    return tf.keras.backend.elu(tf.keras.backend.pow(x, 2), alpha=1.0)
def custom_act_v2(x):
    """Smoother custom activation: x * tanh(x)"""
    return x * tf.keras.backend.tanh(x)

def custom_act_v3(x):
    """Swish-like activation: x * sigmoid(x)"""
    return x * tf.keras.backend.sigmoid(x)

def custom_act_v4(x):
    """Softer version: ELU(x) instead of ELU(x²)"""
    return tf.keras.backend.elu(x, alpha=1.0)

# Define a custom LeakyReLU function if needed
def leaky_relu(x, alpha=0.2):
    """Custom implementation of leaky ReLU activation function."""
    return tf.maximum(alpha * x, x)

# Register the custom activation functions.
tf.keras.utils.get_custom_objects().update({
    'custom_act_v1' : tf.keras.layers.Activation(custom_act_v1),
    'custom_act_v2' : tf.keras.layers.Activation(custom_act_v2),
    'custom_act_v3' : tf.keras.layers.Activation(custom_act_v3),
    'custom_act_v4' : tf.keras.layers.Activation(custom_act_v4)
})

activation_dict = {
    'custom_act_v1': 'custom_act_v1',
    'custom_act_v2': 'custom_act_v2',
    'custom_act_v3': 'custom_act_v3',
    'custom_act_v4': 'custom_act_v4',
    'relu': tf.keras.activations.relu,
    'leaky_relu': leaky_relu,
    'sigmoid': tf.keras.activations.sigmoid,
    'tanh': tf.keras.activations.tanh,
    'elu': tf.keras.activations.elu,
}

#%% ---- Model class ----
class NN_Model (tf.keras.Model):

    def __init__ (self, norm_params, hidden_dims, activation_fn):
        """
        Configure the layers of the model based on the given number of hidden layers (excl. output layer) and nodes.

        Parameters:
        - norm_params_ (list): List of tuples containing the normalization parameters (α, β) for each input feature.
        - hidden_dims (list): List of integers representing the number of nodes in each hidden layer.
        - activation_func (str): Activation function for the hidden layers.
        """
        super().__init__()
        self.num_inputs = 7
        self.num_outputs = 1
        self.activation_fn = activation_fn
        self.norm_params = norm_params

        self.hidden_layers = []
        for i, h_dim in enumerate(hidden_dims):
            self.hidden_layers.append(tf.keras.layers.Dense(
                h_dim,
                activation=activation_fn,
                name=f'hidden_layer_{i+1}',
                dtype=GLOBAL_DTYPE
            ))

        self.out_layer = tf.keras.layers.Dense(self.num_outputs, name='out_layer', dtype=GLOBAL_DTYPE)

        self.build(input_shape=(None, self.num_inputs))
        # self.summary()

    def get_config(self):
        config = super().get_config()
        config.update({
            'norm_params': self.norm_params,
            'hidden_dims': [layer.units for layer in self.hidden_layers],
            'activation_func': self.activation_fn,
        })
        return config

    @classmethod
    def from_config(cls, config):
        return cls(**config)

    def call (self, inputs):
        """
        Process tensor inputs through the network.

        Parameters:
        - inputs: Tensor of shape (batch_size, 8) where:
                  - columns 0-2: plastic strain tensor invariants (eps_pl_I1, eps_pl_I2, eps_pl_I3)
                  - columns 3-7: deviatoric stress tensor components (s_11, s_22, s_12, s_23, s_13)

        Returns:
        - n_psi_max_t: Predicted normalized maximum free energy
        """
        #! NOTE: Separation of the inputs into 2 tensors is just for conceptual understanding, and possibly future extensions.
        # plastic strain invariants (first 2 components)
        inp_eps_pl_tensor = tf.slice(inputs, [0, 0], [-1, 2], name='plastic_strain_invariants')
        # deviatoric stress tensor (next 5 components)
        inp_stress_tensor = tf.slice(inputs, [0, 2], [-1, 5], name='dev_stress_tensor')

        n_concat = tf.concat([inp_eps_pl_tensor, inp_stress_tensor], axis=1, name='concat_tensor_inputs')

        for layer in self.hidden_layers:
            n_concat = layer(n_concat)
        n_psi_max_t = self.out_layer(n_concat)

        return n_psi_max_t

    def infer (self, input_test):
        eqp = tf.slice(input_test, [0, 0], [-1, 1], name='eqp')
        J3 = tf.slice(input_test, [0, 1], [-1, 1], name='J3')
        sigma_dev = tf.slice(input_test, [0, 2], [-1, 5], name='deviatoric_stress_tensor')

        n_eqp = self._Normalize(eqp, self.norm_params[0])
        n_J3 = self._Normalize(J3, self.norm_params[1])
        n_sigma_dev = normalize_stress_components(sigma_dev, eps=1e-8)

        n_concat = tf.concat([n_eqp, n_J3, n_sigma_dev], axis=1, name='concat_test_inputs')

        n_psi_max_t = self.call(n_concat)

        psi_max_t = self._UnNormalize(n_psi_max_t, self.norm_params[2])

        return psi_max_t

    @classmethod
    def train_model (cls, model_instance, train_data, val_data, LearningRate, nEpochs, bSize, silent_training=False, early_stopping=None, lr_schedule_type='constant'):
        """
        Train the model with learning rate scheduling.

        Parameters:
        - model_instance: Instance of NNf_TwoInputs to train
        - train_data: Training data with shape (num_samples, num_features)
        - val_data: Validation data with shape (num_samples, num_features)
        - LearningRate: Initial learning rate for the optimizer
        - nEpochs: Number of epochs to train
        - bSize: Batch size
        - silent_training: If True, suppress training output
        - early_stopping: Early stopping callback
        - lr_schedule_type: Type of learning rate schedule to use ('exponential', 'cosine', or 'constant')

        Returns:
        - history: Training history
        """

        silent_verbose = 0 if silent_training else 1
        if early_stopping is None:
            early_stopping = tf.keras.callbacks.EarlyStopping(monitor='val_loss', patience=150, restore_best_weights=True)

        callbacks = [early_stopping]

        if lr_schedule_type == 'exponential':
            decay_steps = nEpochs // 2  # Decay significantly over 1/2 of training
            decay_rate = 0.85   # Decay the learning rate every decay_steps
            lr_scheduler = tf.keras.optimizers.schedules.ExponentialDecay(
                initial_learning_rate=LearningRate,
                decay_steps=decay_steps,
                decay_rate=decay_rate,
                staircase=False  # Smooth decay
            )
            print(f"Using exponential learning rate decay: initial={LearningRate}, decay_steps={decay_steps}, decay_rate={decay_rate}")

        if lr_schedule_type == 'cosine':
            lr_scheduler = tf.keras.optimizers.schedules.CosineDecay(
                initial_learning_rate=LearningRate,
                decay_steps=nEpochs,
                alpha=0.01          # End at 1% of initial learning rate
            )
            print(f"Using cosine learning rate decay: initial={LearningRate}, decay_steps={nEpochs}, alpha=0.01")

        if lr_schedule_type == 'constant':
            lr_scheduler = LearningRate
            print(f"Using constant learning rate: {LearningRate}")

        # Learning rate scheduler callback to log the learning rate
        class LRLogger (tf.keras.callbacks.Callback):
            def on_epoch_end (self, epoch, logs=None):
                if not silent_training and epoch % 100 == 0:
                    lr = self.model.optimizer.learning_rate
                    if hasattr(lr, '__call__'):
                        print(f"\nEpoch {epoch}: Learning rate: {lr(epoch).numpy():.10f}")
                    else: print(f"\nEpoch {epoch}: Learning rate: {float(lr):.10f}")
        if not silent_training:
            callbacks.append(LRLogger())

        optimizer = tf.keras.optimizers.Nadam(learning_rate=lr_scheduler)
        model_instance.compile(optimizer=optimizer, loss=['mae'], metrics=['mape'])
        history = model_instance.fit(
            x=train_data[:, 0:7],
            y=train_data[:, -1],
            validation_data=(val_data[:, 0:7], val_data[:, -1]),
            epochs=nEpochs,
            batch_size=bSize,
            callbacks=callbacks,
            verbose=silent_verbose
        )

        if not silent_verbose:
            print("\n...Training completed in", len(history.history['loss']), "epochs.")

        return history

    @classmethod
    def save_model (cls, model_instance, dataset_name=None, learning_rate=None, LR_schedule_type=None, num_epochs=None, batch_size=None,
                    norm_params=None, save_dir='saved_models'):
        """
        Save the trained model and its metadata, with automated extraction of internal attributes.

        Parameters:
        - model_instance (NN_Model): The trained model instance to be saved.
        - dataset_name (str): Name of the dataset used for training and validation.
        - norm_params (list): Normalization parameters used for training. Obtained from the "process_data" function.
        """

        os.makedirs(save_dir, exist_ok=True)

        layers_config, custom_activation_details = cls._extract_layer_details(model_instance)
        num_untis_per_layer = [layer['units'] for layer in layers_config]
        num_hidden_layers = len(layers_config) - 1 # Exclude the output layer

        folder_name = cls._generate_model_folder_name(
            dataset_name=dataset_name,
            learning_rate=learning_rate,
            LR_schedule_type=LR_schedule_type,
            num_epochs=num_epochs,
            batch_size=batch_size,
            num_hidden_layers=num_hidden_layers,
            num_units_per_layer=num_untis_per_layer,
            activation=layers_config[0]['activation'] if layers_config else 'Unknown'
        )

        model_folder_path = os.path.join(save_dir, folder_name)
        os.makedirs(model_folder_path, exist_ok=True)
        model_path = os.path.join(model_folder_path, 'model')

        # Save the model using the config
        model_instance.save(model_path)

        cls._save_metadata(model_folder_path, dataset_name, learning_rate, LR_schedule_type, num_epochs, batch_size, layers_config, custom_activation_details, norm_params)

        print(f"\n...Model saved successfully in {model_folder_path}")
        return model_folder_path

    @classmethod
    def load_model (cls, model_dir):
        metadata = cls._load_metadata(model_dir)

        norm_params = metadata.get('Normalizing Parameters', None)
        if norm_params is None:
            raise ValueError("Normalization parameters not found in metadata. Cannot load model.")
        hidden_dims = [layer['units'] for layer in metadata['Layers'] if layer['name'].startswith('hidden_layer')]

        # extract activation function
        if metadata['Custom Activation']:
            activation_name = metadata['Custom Activation']['name']
        else:
            activation_name = metadata['Layers'][0]['activation']   # use the activation function of the first hidden layer

        if activation_name not in activation_dict:
            raise ValueError(f"Activation function '{activation_name}' not found in activation dictionary. Cannot load model.")
        acivation_func = activation_dict[activation_name]

        instance = cls(norm_params=norm_params, hidden_dims=hidden_dims, activation_fn=acivation_func)
        model_path = os.path.join(model_dir, 'model')

        # Load the model using the config
        loaded_model = tf.keras.models.load_model(model_path, custom_objects=activation_dict)

        instance.set_weights(loaded_model.get_weights())

        return instance, metadata

    @staticmethod
    def _Normalize (u, param):
        '''Noramlize/Standardize the input (u)'''
        n_u = tf.divide(tf.math.add(u, -param[1]), param[0])
        return n_u

    @staticmethod
    def _UnNormalize (n_u, param):
        '''Un-noramlize/Un-standardize the input (n_u)'''
        u = tf.add(tf.multiply(n_u, param[0]), param[1])
        return u

    @staticmethod
    def _extract_layer_details (model_instance):
        """ Extract the configuration details of the layers, including their names, units, and activation functions.
        Also, detect and save the source code of custom activation function used in the model.
        """
        layers_config = []
        custom_activation_details = None

        for layer in model_instance.layers:
            if isinstance(layer, tf.keras.layers.Dense):        # Only process Dense layers
                activation_name = None

                # Detect if the activation is custom
                for name, custom_obj in tf.keras.utils.get_custom_objects().items():
                    if hasattr(custom_obj, '__call__') and custom_obj == layer.activation:
                        activation_name = name
                        # Save source code of the custom activation function
                        if custom_activation_details is None:   # Only save the first custom activation found
                            custom_activation_details = {
                                "name": name,
                                "source_code": inspect.getsource(custom_obj)
                            }
                        break

                # Handle standard activations
                if not activation_name:
                    activation_name = layer.activation.__name__ if hasattr(layer.activation, '__name__') else 'unknown'

                # Store layers details
                layer_info = {
                    "name": layer.name,
                    "units": layer.units,
                    "activation": activation_name
                }
                layers_config.append(layer_info)

        return layers_config, custom_activation_details

    @staticmethod
    def _generate_model_folder_name (dataset_name, learning_rate, LR_schedule_type, num_epochs, batch_size, num_hidden_layers, num_units_per_layer, activation):
        # Convert "num_units_per_layer" to the desired format
        num_units_str = "_".join(map(str, num_units_per_layer))
        num_units_formatted = f"({num_units_str})"

        model_attributes = {
            'D': dataset_name or 'Unknown',                     # Dataset name
            'LR': learning_rate or 'Unknown',                   # Learning rate
            'LRT': LR_schedule_type or 'Unknown',               # Learning rate schedule type
            'BS': batch_size or 'Unknown',                      # Batch size
            'E': num_epochs or 'Unknown',                       # Number of epochs
            'HL': num_hidden_layers,                            # Number of Hidden Layers
            'N': num_units_formatted,                           # Number of units
            'ACT': activation or 'Unknown',                     # Activation functions
        }
        folder_name = "_".join([f"{key}{value}" for key, value in model_attributes.items()])
        folder_name = f'{folder_name}_{datetime.now().strftime("%Y-%m-%d_%H-%M-%S")}'
        return folder_name

    @staticmethod
    def _save_metadata (model_folder_path, dataset_name, learning_rate, LR_schedule_type, num_epochs, batch_size, layers_config, custom_activation_details, norm_params):
        # Convert norm_params to a serializable format
        norm_params = norm_params.tolist() if isinstance(norm_params, np.ndarray) else norm_params
        model_metadata = {
            'Dataset name': dataset_name or 'Unknown',          # Name of the dataset used for training and validation.
            'Learning Rate': learning_rate or 'Unknown',
            'Learning Rate Schedule Type': LR_schedule_type or 'Unknown',
            'Batch Size': batch_size or 'Unknown',
            'Num Epochs': num_epochs or 'Unknown',
            'Layers': layers_config,                            # List of dictionaries for each layer, including name, units, and activation
            'Normalizing Parameters': norm_params or None,
            'Custom Activation': custom_activation_details      # Save custom activation function details (if any)
        }

        metadata_path = os.path.join(model_folder_path, 'metadata.json')
        with open(metadata_path, 'w') as metadata_file:
            json.dump(model_metadata, metadata_file, indent=4)

    @staticmethod
    def _load_metadata (model_dir):
        metadata_path = os.path.join(model_dir, 'metadata.json')
        with open(metadata_path, 'r') as metadata_file:
            metadata = json.load(metadata_file)
        return metadata