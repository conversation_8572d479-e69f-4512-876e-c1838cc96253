from plots import Plotter
plotter = Plotter()

from funcs import get_raw_data, compute_psi, compute_invariants, compute_deviatoric, compute_eps_pl, pre_process_data
import numpy as np
train_data_raw = get_raw_data('2.0',2e5)
train_data = pre_process_data(train_data_raw)
val_data = pre_process_data(get_raw_data('2.1',2e5))
test_data = pre_process_data(get_raw_data('2.3',2e5))

# plotter.plot_loading_graphs(train_data_raw)
# plotter.plot_loading_graphs(get_raw_data('2.3',2e5))
# plotter.plot_loading_graphs(get_raw_data('1.4',2e5))
plotter.plot_data_hisograms(train_data, val_data, test_data, figsize=(8, 5), use_log_scale_yaxis=False)
# plotter.plot_data_scatter(train_data, val_data, test_data, axis_dict={'x':0, 'y':2, 'z':7})
plotter.plot_data_scatter(train_data, val_data, test_data, axis_dict={'x':0, 'y':2, 'z':7})

test_raw = get_raw_data('2.0',2e5)
test_data = pre_process_data(test_raw)
np.max(np.diff(test_raw[:, 0], axis=0))

temp_data_raw = get_raw_data('12.3',2e5)
temp_data = pre_process_data(temp_data_raw)

np.isclose(temp_data_raw[temp_data_raw[:, 12]>0][:, 12], temp_data[:, 0])
print(temp_data_raw[temp_data_raw[:, 12]>0][:, 12], temp_data[:, 0])
print()

